from code.llm import LLMProvider
from code.prompt import get_decision, get_story, get_thrill
from code.llm import RepairedLLMOutput
from pydantic import BaseModel, Field
import asyncio

async def generate_decision(npc, player, outfits, history, model, skill, sys_prompt, usr_prompt=None):
    """生成初始对话和选项"""
    class Message(RepairedLLMOutput):
        role: str = Field(
            description="说话角色，只能为npc, player, voiceover中的一个"
        )
        content: str = Field(
            description="说话内容"
        )
        mood: str = Field(
            description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
        )

    class Branch(BaseModel):
        answer: str = Field(description="玩家的行动/决策/回复内容")
        mood: str = Field(
            description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
        )
        reaction: list[Message] = Field(
            description="做出该行动/决策/回复后对应的后续剧情"
        )

    class DecisionScene(BaseModel):
        decision_scene: Message = Field(description="令玩家纠结的决策场景")
        branches: list[Branch] = Field(
            description="给玩家的三个可选行动/决策/回复内容以及对应的后续剧情"
        )
    
    messages = get_decision(
        npc, player, outfits, history, skill, sys_prompt, usr_prompt
    )
    client = LLMProvider()
    try:
        response = await client.asyncinfer(messages=messages, response_model=DecisionScene, model=model)
    except Exception as e:
        return f"出现错误{e}", []
    
    return response["decision_scene"], response["branches"]

async def generate_story(npc, player, place, outfits, history, model, skill, sys_prompt, usr_prompt=None):
    """根据技能生成新一幕剧情"""
    class Message(RepairedLLMOutput):
        role: str = Field(
            description="说话角色，只能为npc, player, voiceover中的一个"
        )
        content: str = Field(
            description="说话内容"
        )
        mood: str = Field(
            description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
        )

    class Story(RepairedLLMOutput):
        story: list[Message] = Field(
            description="根据技能产生的新一幕剧情"
        )
    
    messages = get_story(
        npc, player, place, outfits, history, skill, sys_prompt, usr_prompt
    )
    client = LLMProvider()
    try:
        response = await client.asyncinfer(messages=messages, response_model=Story, model=model)
    except Exception as e:
        return [{"role": "voiceover", "content": f'出现错误：{e}'}]
    return response['story']

async def generate_thrill(npc, player, place, outfits, model, sys_prompt, usr_prompt=None):
    """根据装扮、地点、男嘉宾人设生成心动值"""
    class Comment_and_Thrill(BaseModel):
        comment: str = Field(
            description="NPC的角度说出对玩家在场景中装扮的第一印象"
        )
        thrill: int = Field(
            description="根据规则计算得到的心动值，0~100之间的整数"
        )
    
    messages = get_thrill(
        npc, player, place, outfits, sys_prompt, usr_prompt
    )
    client = LLMProvider()
    try:
        response = await client.asyncinfer(messages=messages, response_model=Comment_and_Thrill, model=model)
    except Exception as e:
        return 50, f'出现错误：{e}'
    return response['thrill'], response['comment']
