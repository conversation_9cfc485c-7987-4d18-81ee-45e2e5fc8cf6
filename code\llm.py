import json
from code.config import Config

import instructor
from openai import Async<PERSON>penA<PERSON>, OpenAI
from pydantic import BaseModel, ValidationError
from abc import ABC
from typing import Any
import json_repair


class LLMProvider:
    OPENAI_BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    OPENAI_API_KEY = "sk-uEy2hMGNKxG86H7yMgO1N9xG6D1UzQLwOSvPocDDcNaum82L"

    def __init__(self, provider: str = "openailike", need_async: bool = True):
        self._provider = provider
        self._need_async = need_async

        if self._provider == "openailike":
            if self._need_async:
                self._client = instructor.from_openai(
                    AsyncOpenAI(
                        base_url=self.OPENAI_BASE_URL, api_key=self.OPENAI_API_KEY
                    ),
                    mode=instructor.Mode.JSON_SCHEMA,
                )
            else:
                self._client = instructor.from_openai(
                    OpenAI(base_url=self.OPENAI_BASE_URL, api_key=self.OPENAI_API_KEY),
                    mode=instructor.Mode.JSON,
                )
        else:
            # TODO：其他LLM Provider请自行实现
            raise NotImplementedError("Only support openailike provider now.")

    async def asyncinfer(
        self,
        model: str = None,
        prompt: str = None,
        messages: list = None,
        response_model: BaseModel = None,
        max_tokens: int = 1024,
        max_retries: int = 3,
        **kwargs,
    ):
        if messages is None:
            messages = [
                {
                    "role": "user",
                    "content": prompt,
                },
            ]
        response = await self._client.chat.completions.create(
            max_tokens=max_tokens,
            model=model,
            response_model=response_model,
            messages=messages,
            max_retries=max_retries,
            **kwargs,
        )
        if response_model:
            return json.loads(response.json())
        else:
            return response

    def infer(
        self,
        model: str = None,
        prompt: str = None,
        messages: list = None,
        response_model: BaseModel = None,
        max_tokens: int = 1024,
        max_retries: int = 3,
        **kwargs,
    ):
        if messages is None:
            messages = [
                {
                    "role": "user",
                    "content": prompt,
                },
            ]
        response = self._client.chat.completions.create(
            max_tokens=max_tokens,
            model=model,
            response_model=response_model,
            messages=messages,
            max_retries=max_retries,
            **kwargs,
        )
        if response_model:
            return json.loads(response.json())
        else:
            return response

class RepairedLLMOutput(BaseModel):
    @classmethod
    def model_validate_json(
        cls,
        json_data: str | bytes | bytearray,
        *,
        strict: bool | None = None,
        context: Any | None = None,
        by_alias: bool | None = None,
        by_name: bool | None = None,
    ):
        json_data_ = json_repair.repair_json(json_data)
        try:
            # print(f"原始 JSON 数据: {json_data}")
            res = super().model_validate_json(
                json_data_,
                strict=strict,
                context=context,
                by_alias=by_alias,
                by_name=by_name,
            )
            return res
        except ValidationError as e:
            # 打印完整的错误信息
            print(f"验证错误: {e}")
            
            # 打印原始输入数据
            print(f"原始 JSON 数据: {json_data}")
            
            # 打印错误详情（包含输入值）
            for error in e.errors():
                print(f"错误位置: {error['loc']}")
                print(f"错误类型: {error['type']}")
                print(f"错误详情: {error['msg']}")
                print(f"输入值: {error.get('input', '未提供')}")  # 这会显示完整的 input_value
            raise


# class LLMProvider:
#     def __init__(self, provider="openailike"):
#         self.provider = provider

#     async def infer(
#         self,
#         model: str = None,
#         prompt: str = None,
#         messages: list = None,
#         response_model: BaseModel = None,
#         max_tokens=1024,
#         **kwargs,
#     ):
#         if self.provider == "openailike":
#             client = instructor.from_openai(
#                 AsyncOpenAI(
#                     base_url=Config.OPENAI_BASE_URL, api_key=Config.OPENAI_API_KEY
#                 ),
#                 mode=instructor.Mode.JSON,
#             )

#             if messages is None and prompt is None:
#                 raise ValueError("messages and prompt are both None")
#             if messages is None and prompt is not None:
#                 messages = [
#                     {
#                         "role": "user",
#                         "content": prompt,
#                     },
#                 ]

#             response = await client.chat.completions.create(
#                 max_tokens=max_tokens,
#                 model=model,
#                 response_model=response_model,
#                 messages=messages,
#                 max_retries=1,
#                 **kwargs,
#             )
#             if response_model:
#                 return json.loads(response.json())
#             else:
#                 return response
#         else:
#             # TODO：其他LLM Provider请自行实现
#             raise NotImplementedError("Only support openailike provider now.")
