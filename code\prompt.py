def get_decision(npc, player, outfits, history, skill, sys_prompt, usr_prompt=None):
    """生成决策场景"""
    history_str = '\n'.join([
        f"{str(h)}" for h in history
    ])
    player_outfit_str = ', '.join([
        f"{part}: {outfit['name']}" for part, outfit in outfits.items()
    ]) if len(outfits) > 0 else ""
    
    user_prompt = f"""玩家选择的技能（可能为空）：{skill}

NPC信息:
姓名: {npc['name']}

人设: 
{npc['bio']}

玩家信息:
姓名: {player['name']}
装扮: {player_outfit_str}

历史剧情:
{history_str}

请根据要求和指示，生成一个富有戏剧张力的决策场景和三个将把游戏推向不同方向的可选项。"""

    return [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": user_prompt if usr_prompt is None else user_prompt}
    ]

def get_story(npc, player, place, outfits, history, skill, sys_prompt, usr_prompt=None):
    """生成情景对话"""
    history_str = '\n'.join([
        f"{str(h)}" for h in history
    ]) if len(history) > 0 else ""
    player_outfit_str = ', '.join([
        f"{part}: {outfit['name']}" for part, outfit in outfits.items()
    ]) if len(outfits) > 0 else ""
    user_prompt = f"""玩家选择的技能（可能为空）：{skill}

故事发生地点：{place['name']}
{place['description_model']}

NPC信息:
姓名: {npc['name']}

人设: 
{npc['bio']}

玩家信息:
姓名: {player['name']}
装扮: {player_outfit_str}

历史剧情:
{history_str}

请根据要求和指示，生成一幕精彩绝伦的剧情。"""
    return [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": user_prompt if usr_prompt is None else user_prompt}
    ]

def get_thrill(npc, player, place, outfits, sys_prompt, usr_prompt=None):
    """生成情景对话"""
    player_outfit_str = ', '.join([
        f"{part}: {outfit['name']}" for part, outfit in outfits.items()
    ]) if len(outfits) > 0 else ""
    user_prompt = f"""故事发生地点：{place['name']}
{place['description_model']}

NPC人设: 
{npc['bio']}

玩家装扮: 
{player_outfit_str}

请根据要求和指示，写出男NPC对女主的心动值的推理和推理结果。"""
    return [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": user_prompt if usr_prompt is None else user_prompt}
    ]
