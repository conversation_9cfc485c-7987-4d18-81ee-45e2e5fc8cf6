import json
import random
from typing import List, Dict, Any, Optional

def load_data(file_path: str) -> Any:
    """加载JSON数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return []
    except json.JSONDecodeError:
        print(f"文件 {file_path} 格式错误")
        return []

def save_data(data: Any, file_path: str) -> None:
    """保存数据到JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"保存文件 {file_path} 失败: {e}")

def roll_random_outfits(outfits_data: Dict[str, List[Dict]], num: int) -> Dict[str, Dict]:
    """为每个部位随机抽取指定数量的装扮"""
    selected_outfits = {}
    for part, outfits in outfits_data.items():
        if outfits:  # 确保该部位有装扮
            selected = random.choice(outfits)
            selected_outfits[part] = selected
    return selected_outfits
