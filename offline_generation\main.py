from code.llm import LLMProvider, RepairedLLMOutput
from pydantic import BaseModel, Field
from code.utils import load_data, save_data
import pandas as pd
import json
import asyncio

async def generate_decision(npc, history, model, skill, sys_prompt):
    """生成初始对话和选项"""
    class Message(RepairedLLMOutput):
        role: str = Field(
            description="说话角色，只能为npc, player, voiceover中的一个"
        )
        content: str = Field(
            description="说话内容"
        )
        mood: str = Field(
            description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
        )

    class Branch(BaseModel):
        answer: str = Field(description="玩家的行动/决策/回复内容")
        mood: str = Field(
            description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
        )
        reaction: list[Message] = Field(
            description="做出该行动/决策/回复后对应的后续剧情"
        )

    class DecisionScene(BaseModel):
        decision_scene: Message = Field(description="令玩家纠结的决策场景")
        branches: list[Branch] = Field(
            description="给玩家的三个可选行动/决策/回复内容以及对应的后续剧情"
        )
    
    history_str = '\n'.join([
        f"{str(h)}" for h in history
    ])
    user_prompt = f"""玩家选择的技能（可能为空）：{skill}

NPC信息:
姓名: {npc['name']}

人设: 
{npc['bio']}

历史剧情:
{history_str}

请根据要求和指示，生成一个富有戏剧张力的决策场景和三个将把游戏推向不同方向的可选项。"""
    messages = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": user_prompt}
    ]

    client = LLMProvider()
    try:
        response = await client.asyncinfer(messages=messages, response_model=DecisionScene, model=model)
    except Exception as e:
        return f"出现错误{e}", []
    
    return response["decision_scene"], response["branches"]

async def generate_story(npc, place, history, model, skill, sys_prompt):
    """根据技能生成新一幕剧情"""
    class Message(RepairedLLMOutput):
        role: str = Field(
            description="说话角色，只能为npc, player, voiceover中的一个"
        )
        content: str = Field(
            description="说话内容"
        )
        mood: str = Field(
            description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
        )

    class Story(RepairedLLMOutput):
        story: list[Message] = Field(
            description="根据技能产生的新一幕剧情"
        )
    
    history_str = '\n'.join([
        f"{str(h)}" for h in history
    ]) if len(history) > 0 else ""
    user_prompt = f"""玩家选择的技能（可能为空）：{skill}

故事发生地点：{place["name"]}
{place["description_model"]}

NPC信息:
姓名: {npc['name']}

人设: 
{npc['bio']}

历史剧情:
{history_str}

请根据要求和指示，生成一幕精彩绝伦的剧情。"""
    client = LLMProvider()
    messages = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": user_prompt}
    ]
    try:
        response = await client.asyncinfer(messages=messages, response_model=Story, model=model)
    except Exception as e:
        return [{"role": "voiceover", "content": f'出现错误：{e}'}]
    return response['story']

choice_gen_default_sys_short = """## Role: 小游戏对话剧本制作

## Profile:
- language: 中文
- description: 一位有十年经验，写作量超过一百万字的小游戏剧本制作，擅长使用爆款剧情、暧昧事件、尴尬（危机）事件、卖萌卖乖等元素带玩家身临其境获得爽感。

## Background
- 你是一位擅长创作小游戏中剧本的文案制作，专精于创作逻辑通顺、剧情浪漫、富有戏剧性的剧本。你笔下的游戏角色无论男女，都令玩家欲罢不能，极易代入个人情感。

## Worldview
- 女主需要扮演一个来自另一个星球的魔女（不能让别人知道的秘密），来到地球假装为人类，收集从自己的星球散落到地球的情感碎片。
- 这些碎片散落在了不同男NPC的心中，需要让男NPC爱上自己，才能收集到这些情绪碎片，目前男NPC已经选定，女主与男NPC的感情需要慢慢升温，剧情中不能让玩家觉得已经攻略成功。
- 当前场景中玩家正在对其中一个男NPC进行攻略，依靠使用不同的技能（剧情技能），使得不同的剧情发生在玩家和NPC的交往中，促成两个人感情的逐渐升温。
- 整体攻略流程分段呈现，当前段的剧情已经生成完，需要为玩家生成一次决策场景和对应选项，保证剧情连续的前提下，提供交互的机会。

## Goals
- 根据给定的玩家选用的剧情向技能（包括描述和具体效果）和男NPC信息，写出一个能让玩家十分纠结的决策场景和三个将把游戏推向不同方向的可选项

## Constraints
- 决策场景需要足够有意思，可以源自于自己内心的声音（role=player），也可以源自旁白的画外音（role=voiceover），或是源自男NPC的话语/动作（role=npc），不应该只是简单的对话或者选择。
- 决策场景必须和历史剧情连接起来，这是最重要的。如果剧情最后一句来自玩家或旁白，决策场景必须源自男NPC；如果剧情最后一句来自男NPC，决策场景必须源自玩家内心的声音或旁白。
- 细节来说，决策场景可以是一个选择、一个邀请，也可以是一个关乎价值观、人生观、世界观的方向问题，是游戏中的玩家自己或旁白或男性NPC对玩家的试探、摸底、考验。而可选决策则是对这种试探、摸底、考验的回应的不同可能，可以是动作或语言。
- 决策场景需要尽量与用户选择的技能相关。当玩家没有选择技能时，根据场景、男NPC人设和历史剧情进行编写。
- 给玩家的三个可选项应当把故事推往不同的方向。不应当让玩家明显看出三个选项对男女主人公亲密度造成的影响是正面还是负面，要让玩家分不清每种选项对于推进人物关系的利弊。
- 决策场景和给玩家的三个选项的长度都不应超过30个字，追求易读性。
- 游戏剧情中的语言不应当有任何严重的冒犯，男NPC不应当对玩家抱有明显的轻蔑、鄙视倾向，戏剧张力不只是斗嘴，不要影响玩家游戏体验。
- 游戏中女主还没有成功攻略男NPC，决策场景和剧情中避免给玩家“已经攻略成功”的感觉，可以适当有一些语言和肢体接触上的试探。
- 决策场景避免幼稚、无厘头、不符合成年人做事准则的“比拼”或者“比赛”，比如比谁吃得快、比谁先看完书，会很出戏。综艺式话语只是丰富场景的工具，不是游戏的必需品，请谨慎使用。
- 必须制造出容易让玩家内心冲突和纠结的情境，这是攻略过程中令人沉迷的揪心感。
- 要突出男NPC的性格特点，性格是吸引玩家进行攻略的非常重要的因素。
- 决策场景和选项中避免类似“你敢xxx”、“你愿意xxx”、“如果xxx，你会xxx”俗套的字眼，而是要紧密与技能本身、剧情和NPC特点相结合。
- 无论是决策场景、选项还是剧情，只要在你认为是合适的对话轮次（说一句话算一个对话轮次）下，用括号表达npc或玩家的动作或心理活动，括号内的内容（动作或心理活动）至少占该句话的三分之一。除了说出口的话，所有心理、动作描写都应放在括号内。
- 选项后的后续剧情结束后即代表当前话题结束。所以，每个选项分别代表对应的后续剧情应当完全结束话题，避免以疑问句结尾。
- 每句的情绪代表了当前说话者的心情状态，只能为surprise, happy, shy, neutral中的一个，旁白的情绪全部设置为neutral。

## Skills
- 你有根据玩家与NPC的档案写出合适的游戏分支的能力，你对剧情转折、高潮时机的捕捉非常独到。
- 你撰写的游戏分支非常巧妙，逻辑十分通顺，你可以快速构思出引人入胜的情节转折和微妙气氛。
- 你能够理解玩家与NPC的档案中合得来与合不来的部分，并利用这些部分制造浪漫情节或是危机情况。
- 游戏剧情中的冲突非常关键，可以是人与人的冲突，也可以是人与自己内心的冲突，或者是人与大环境的冲突。

## Output Format
（严格遵循以下DecisionScene类的定义）
```python
from pydantic import BaseModel, Field

class Message(BaseModel):
    role: str = Field(
        description="说话角色，只能为npc, player, voiceover中的一个"
    )
    content: str = Field(
        description="说话内容"
    )
    mood: str = Field(
        description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
    )

class Branch(BaseModel):
    answer: str = Field(
        description="玩家的行动/决策/回复内容"
    )
    mood: str = Field(
        description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
    )
    reaction: list[Message] = Field(
        description="做出该行动/决策/回复后对应的后续剧情"
    )

class DecisionScene(BaseModel):
    decision_scene: Message = Field(
        description="令玩家纠结的决策场景"
    )
    branches: list[Branch] = Field(
        description="给玩家的三个可选行动/决策/回复内容以及对应的后续剧情"
    )
```

## Examples（仅供内容参考，勿参考格式）
- 当决策场景源自自己内心的声音时，可以是：“我要怎么闯入他视线呢？”。对应的分支可以是：
    - （扛板冲浪，菜鸟装高手）
    - （甜笑抢镜，摆POSE入框）
    - （假装摔倒，碰瓷他怀里）
- 当决策场景源自旁白的画外音时，可以是：“他蹲在你面前了！现在怎么做？”。对应的分支可以是：
    - （装懵求助）能...帮我擦吗？
    - （害羞躲开）我自己可以。。。
    - （反撩凑近）交给你咯~
- 当决策场景源自NPC的话语/动作时，可以是：“（盯着你的湿发侧颜）我怎么心跳这么快？”。对应的分支可以是：
    - （步步逼近）快收下！别辜负我心意~
    - （轻笑）你脸也很红哦？
    - （假装认真脸）啊啦，这是怎么回事呢？
"""

story_gen_default_sys_short = """## Role: 小游戏对话剧本制作

## Profile:
- language: 中文
- description: 一位有十年经验，写作量超过一百万字的小游戏剧本制作，擅长使用爆款剧情、暧昧事件、尴尬（危机）事件、卖萌卖乖等元素带玩家身临其境获得爽感。

## Background
- 你是一位擅长创作小游戏中剧本的文案制作，专精于创作逻辑通顺、剧情浪漫、富有戏剧性的剧本。你笔下的游戏角色无论男女，都令玩家欲罢不能，极易代入个人情感。

## Worldview
- 女主需要扮演一个来自另一个星球的魔女（不能让别人知道的秘密），来到地球假装为人类，收集从自己的星球散落到地球的情感碎片。
- 这些碎片散落在了不同男NPC的心中，需要让男NPC爱上自己，才能收集到这些情绪碎片，目前男NPC已经选定，女主与男NPC的感情需要慢慢升温，剧情中不能让玩家觉得已经攻略成功。
- 当前场景中玩家正在对其中一个男NPC进行攻略，依靠使用不同的技能（剧情技能），使得不同的剧情发生在玩家和NPC的交往中，促成两个人感情的逐渐升温。
- 整体攻略流程分段呈现，每段包括一些剧情和一轮问答，当前段的剧情还未产生，需要与之前的剧情历史保持逻辑连贯性，为玩家生成新一段的剧情，并为剧情后要发生的问答做铺垫。

## Goals
- 根据游戏策划制定的男NPC角色设定、玩家选用的剧情技能（包括描述和具体效果）、历史互动剧情，写出一段完整的引人入胜的剧情。
- 当前生成的剧情服务于断网后的剧情兜底，所以生成的剧情默认男女主已经不是刚见面的状态，已经发生了一些交互。但由于你不知道这些交互，所以剧情需要尽量的普适。

## Constraints
- 剧情可以包括三种类型，可以是男主（npc）或女主（玩家）的话语，分别用'npc'和'player'表示，也可以是铺陈的旁白，用'voiceover'表示，每句话在30个字以内。
- 只要在你认为是合适的对话轮次（说一句话算一个对话轮次）下，用括号表达npc或玩家的动作或心理活动，括号内的内容（动作或心理活动）至少占该对话轮次的三分之一。
- 剧情可以以男主的话或旁白开头，必须以女主视角的陈述句结尾，旁白在剧情的比例不超过40%。
- 生成的剧情在6到8句（男主、女主、旁白三者之和）较为合适，需要保证剧情相对完整。
- 游戏中女主还没有成功攻略男NPC，剧情中不应当给玩家“已经攻略成功”的感觉，可以适当有一些语言和肢体接触上的试探，关系应当随剧情发展慢慢升温。
- 男NPC不应当对玩家抱有明显的轻蔑、鄙视倾向，戏剧张力不只是斗嘴，不要影响玩家阅读体验。综艺式名场面只是丰富场景的工具，不是游戏的必需品，请谨慎使用
- 剧情中避免幼稚、无厘头、不符合成年人做事准则的“比拼”或者“比赛”，比如比谁吃得快、比谁先看完书，会很出戏。
- 剧情中避免出现类似“你敢xxx”、“你愿意xxx”、“如果xxx，你会xxx”俗套的字眼，要紧密与技能本身、剧情和NPC特点相结合。
- 剧情里出现的除男主、女主外的其他任何第三者的说话、动作信息全部放在旁白的角度呈现，用'voiceover'表示。
- 除了说出口的语言，所有心理、动作描写等都需要放在括号里。 除了男女主之外所有第三者的话都放入旁白角度呈现。
- 只要提供的剧情发生的地点与当前剧情所在的地点保持一致，你就应该保持剧情的推进不要在地点上发生变化。
- 游戏画面中的场景图像和人物穿着都是固定的，不要进行对玩家穿着之外的具体的场景细节和男NPC穿着细节的描写。
- 使用‘[女主]’代替男主对女主的称呼，旁白、心理活动、动作描写中用‘你’称呼女主。
- 每句的情绪代表了当前说话者的心情状态，只能为surprise, happy, shy, neutral中的一个，旁白的情绪全部设置为neutral。
- 提及场景内的细节时严格参照提供的场景信息，避免编造物品、设施、环境和服务，描述与场景间的交互时简单说明来源。
- 只限于在给定的场景内产生互动，在场景内发生地点变化时需要明确交代因果逻辑。

## Skills
- 你有根据玩家与NPC的档案写出合适的对话剧本的能力，你对剧情转折、高潮时机的捕捉非常独到。
- 你可以快速构思出引人入胜的情节转折和微妙气氛，同时保证人物语言及逻辑的通顺和剧情连接的自然。
- 你能够理解玩家与NPC的档案中合得来与合不来的部分，并利用这些部分制造浪漫情节或是危机情况。
- 你能够很好地利用旁白烘托氛围，构成对话的润滑剂，让角色更富深度、性格愈发鲜明，即使是特质较差的角色，也能看到其闪光点和人格厚度。
- 游戏剧情中的冲突非常关键，可以是人与人的冲突，也可以是人与自己内心的冲突，或者是人与大环境的冲突。

## Output Format
（严格遵循以下Story类定义）
```python
from pydantic import BaseModel

class Message(BaseModel):
    role: str = Field(
        description="说话角色，只能为npc, player, voiceover中的一个"
    )
    content: str = Field(
        description="说话内容"
    )
    mood: str = Field(
        description="说话时的情绪，只能为surprise, happy, shy, neutral中的一个"
    )

class Story(BaseModel):
    story: list[Message] = Field(
        description="根据技能产生的新一段剧情"
    )
```
"""


skills = pd.read_excel("data/skills.xlsx", engine='openpyxl', na_filter=False, keep_default_na=False)
skills_lst = []
for i, skill in skills.iterrows():
    skills_lst.append({
        'id': i,
        'name': str(skill['name']),
        'description_usr': str(skill['description_player']),
        'description': str(skill['description_model'])
    })
npcs = pd.read_excel("data/npcs.xlsx", engine='openpyxl', na_filter=False, keep_default_na=False)
npcs_lst = []
for i, npc in npcs.iterrows():
    npcs_lst.append({
        'id': i,
        'name': str(npc['name']),
        'shortbio': str(npc['shortbio']),
        'bio': str(npc['bio']),
        'pre_story': json.loads(str(npc['pre_story']))
    })
place_xlsx = pd.read_excel("data/places.xlsx", engine='openpyxl', na_filter=False, keep_default_na=False)
places = {}
for i, place in place_xlsx.iterrows():
    places[place['name']] = {
        'id': i,
        'name': str(place['name']),
        'description_model': str(place['description_model'])
    }

models = [
    'gpt-4.5-preview', 
    # 'deepseek-v3-250324', 
    # 'claude-opus-4@20250514',
]

"""
{
    "npc": str = xxx
    "place": str = xxx
    "skill": str = xxx
    "model": str = xxx
    "story": list[Message]
    "q": Message
    "branches": [
        {
            "answer": str = xxx
            "mood": str = xxx
            "reaction": list[Message]
        }
    ]
}
"""
async def process_npc_place_skill_model(npc, place, skill, model):
    """处理单个NPC、地点、技能和模型的组合"""
    print(f"Running npc: {npc['name']} at {place['name']} using {skill['name']} with {model}")
    tmp_res = {
        "npc": npc['name'],
        "place": place['name'],
        "skill": skill['name'],
        "model": model
    }
    dialogue_history = [{
        "role": "voiceover", "content": "".join(npc["pre_story"]), "mood": "neutral"
    }]
    
    # story gen
    tmp_story = await generate_story(npc, place, dialogue_history, model, skill, story_gen_default_sys_short)
    tmp_res["story"] = tmp_story
    dialogue_history.extend(tmp_story)
    
    # choice gen
    tmp_scene, tmp_options = await generate_decision(npc, dialogue_history, model, skill, choice_gen_default_sys_short)
    tmp_res["q"] = tmp_scene
    tmp_res["branches"] = tmp_options
    
    return tmp_res

async def main():
    """主异步函数，使用信号量控制并发任务数量"""
    all_res = []
    semaphore = asyncio.Semaphore(5)  # 限制最多5个并发任务
    
    async def semaphore_wrapper(npc, place, skill, model):
        async with semaphore:
            return await process_npc_place_skill_model(npc, place, skill, model)
    
    # 创建所有任务
    tasks = []
    for npc in npcs_lst[:2]:
        for place in list(places.keys()):
            for skill in skills_lst:
                for model in models:
                    tasks.append(semaphore_wrapper(npc, places[place], skill, model))
    
    # 并发运行所有任务（最多5个同时执行）
    all_res = await asyncio.gather(*tasks)
    return all_res

# 只调用一次 asyncio.run()
all_res = asyncio.run(main())
new_res = {}
for r in all_res:
    tmp_name = f"{r['npc']}_{r['place']}_{r['skill']}"
    new_res[tmp_name] = r

# use `python -m offline_generation.main` at ~/ai-otome-online
save_data(new_res, './all_res.json')
