import os
import json
import asyncio
import time
from datetime import datetime
import argparse
from code.config import Config
from code.utils import load_data, roll_random_tags, roll_random_outfits, save_data
from code.agent import generate_dialogue, generate_voiceover, generate_story

# 模拟Streamlit的session_state
class SessionState:
    def __init__(self):
        self.game_state = 'intro'
        self.selected_npc = None
        self.player_tags = []
        self.player_outfits = {}
        self.tag_rolls = 0
        self.outfit_rolls = 0
        self.current_tags = []
        self.current_outfits = {}
        self.max_decisions = 0
        self.dialogue_history = []
        self.current_dialogue = ""
        self.current_choices = []
        self.player = {"id": '0', "name": "李飘雪", "bio": "一位神秘的旅行者"}
        self.chat_rounds = 0
        self.chat_model = None
        self.place = None

# 加载游戏数据
def load_game_data():
    tags = load_data("data/tags.json")
    npcs = load_data("data/npcs.json")
    outfits = load_data("data/outfits.json")
    players = load_data("data/players.json")
    
    # 确保玩家数据存在
    player = players[0] if players else {"id": '0', "name": "玩家", "bio": "一位神秘的旅行者"}
    
    return tags, npcs, outfits, player

# 离线跑测单个组合
async def offline_test_combination(chat_model, place, npc_index, npcs, tags):
    # try:
    # 初始化
    _, _, outfits_data, player = load_game_data()
    tags_dict = {t["id"]: t for t in tags}
    
    # 确保npc_index有效
    if npc_index < 0 or npc_index >= len(npcs):
        raise ValueError(f"NPC序号无效，有效范围: 0-{len(npcs)-1}")
    
    # 初始化会话状态
    st_session = SessionState()
    st_session.player = player
    st_session.chat_model = chat_model
    st_session.place = place
    
    # 创建logs目录（如果不存在）
    if not os.path.exists("offline_logs"):
        os.makedirs("offline_logs")
    
    print(f"\n==== 开始跑测组合: 模型={chat_model}, 地点={place}, NPC序号={npc_index} ({npcs[npc_index]['name']}) ====")
    
    # 游戏流程自动化
    while st_session.game_state != 'end':
        if st_session.game_state == 'intro':
            st_session.game_state = 'global_setting'
        
        elif st_session.game_state == 'global_setting':
            st_session.max_decisions = 5
            st_session.game_state = 'npc_selection'
        
        elif st_session.game_state == 'npc_selection':
            st_session.selected_npc = npcs[npc_index]
            st_session.game_state = 'intro_generation'
        
        elif st_session.game_state == 'intro_generation':
            intro, scenes = await generate_story(
                st_session.place,
                st_session.max_decisions,
                st_session.selected_npc,
                tags,
                st_session.chat_model,
            )
            
            st_session.intro = intro.replace('[女主]', st_session.player['name'])
            st_session.scenes = [s.replace('[女主]', st_session.player['name']) for s in scenes]
            
            first_scene = await generate_voiceover(
                st_session.selected_npc,
                st_session.player,
                tags,
                st_session.player_outfits,
                st_session.dialogue_history,
                st_session.chat_model,
                [st_session.scenes[st_session.chat_rounds], st_session.scenes[st_session.chat_rounds+1]]
            )
            
            st_session.dialogue_history.extend(first_scene)
            
            if st_session.max_decisions != len(st_session.scenes):
                st_session.max_decisions = len(st_session.scenes)
            
            st_session.scenes.append("游戏结束")
            st_session.game_state = 'tag_selection'
        
        elif st_session.game_state == 'tag_selection':
            while st_session.tag_rolls < Config.MAX_TAG_ROLLS and not st_session.current_tags:
                st_session.current_tags = roll_random_tags(tags, Config.TAGS_PER_ROLL)
                st_session.tag_rolls += 1
            
            st_session.player_tags = [tag['id'] for tag in st_session.current_tags]
            st_session.player['tags'] = st_session.player_tags
            st_session.game_state = 'outfit_selection'
        
        elif st_session.game_state == 'outfit_selection':
            while st_session.outfit_rolls < Config.MAX_OUTFIT_ROLLS and not st_session.current_outfits:
                st_session.current_outfits = roll_random_outfits(outfits_data, Config.OUTFITS_PER_ROLL)
                st_session.outfit_rolls += 1
            
            st_session.player_outfits = st_session.current_outfits
            st_session.game_state = 'background'
        
        elif st_session.game_state == 'background':
            st_session.game_state = 'decision_count'
        
        elif st_session.game_state == 'decision_count':
            st_session.game_state = 'history'
        
        elif st_session.game_state == 'history':
            dialogue, choices = await generate_dialogue(
                st_session.selected_npc,
                st_session.player,
                tags,
                st_session.player_outfits,
                st_session.dialogue_history,
                st_session.chat_model,
                [st_session.scenes[st_session.chat_rounds], st_session.scenes[st_session.chat_rounds+1]]
            )
            
            st_session.current_dialogue = dialogue
            st_session.current_choices = choices
            st_session.game_state = 'dialogue'
            st_session.dialogue_history.append({"npc": dialogue})
        
        elif st_session.game_state == 'dialogue':
            if st_session.current_choices:
                choice = st_session.current_choices[0]
                st_session.dialogue_history.append({"player": choice})
                st_session.chat_rounds += 1
                
                if st_session.chat_rounds >= st_session.max_decisions:
                    st_session.game_state = 'end'
                    continue
                
                followup_dialogue = await generate_voiceover(
                    st_session.selected_npc,
                    st_session.player,
                    tags,
                    st_session.player_outfits,
                    st_session.dialogue_history,
                    st_session.chat_model,
                    [st_session.scenes[st_session.chat_rounds], st_session.scenes[st_session.chat_rounds+1]]
                )
                
                st_session.dialogue_history.extend(followup_dialogue)
                
                print("dialogue_history: ", st_session.dialogue_history)
                next_dialogue, next_choices = await generate_dialogue(
                    st_session.selected_npc,
                    st_session.player,
                    tags,
                    st_session.player_outfits,
                    st_session.dialogue_history,
                    st_session.chat_model,
                    [st_session.scenes[st_session.chat_rounds], st_session.scenes[st_session.chat_rounds+1]]
                )
                st_session.dialogue_history.append({"npc": next_dialogue})
                
                st_session.current_dialogue = next_dialogue
                st_session.current_choices = next_choices
            else:
                st_session.game_state = 'end'
        
        # 等待一段时间，避免请求过于频繁
        time.sleep(0.5)
    
    # 保存游戏记录
    log_data = {
        "input_params": {
            "chat_model": chat_model,
            "place": place,
            "npc_index": npc_index,
            "npc_name": st_session.selected_npc['name']
        },
        "intro": st_session.intro,
        "scenes": st_session.scenes,
        "player": st_session.player,
        "player_tags": [tags[tag_id] for tag_id in st_session.player_tags if tag_id < len(tags)],
        "player_outfits": st_session.player_outfits,
        "dialogue_history": st_session.dialogue_history,
        "end_time": datetime.now().strftime("%Y%m%d-%H%M%S")
    }
    
    # 生成文件名并保存
    safe_chat_model = chat_model.replace('/', '_').replace(':', '_')
    safe_place = place.replace('/', '_').replace(':', '_')
    file_path = f"offline_logs/{safe_chat_model}_{safe_place}_{npc_index}.json"
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(log_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 跑测完成，结果已保存至: {file_path}")
    return file_path, True
    
    # except Exception as e:
    #     print(f"❌ 跑测组合失败 (模型={chat_model}, 地点={place}, NPC序号={npc_index}): {str(e)}")
    #     return None, False

# 主函数
async def main():
    # 定义可选参数范围
    model_options = ['gpt-4.1', 'gpt-4o-mini', 'gpt-4o', 'Doubao-1.5-pro-32k-character-250228']
    place_options = ['高端商场', '海边沙滩', '登山途中', '博物馆', '咖啡厅', '游泳池', '公园', '大学', '演唱会体育馆', '连锁便利店']
    
    # 加载NPC数据以获取数量
    _, npcs, _, _ = load_game_data()
    npc_options = list(range(len(npcs)))
    
    # 加载标签数据，供所有组合使用
    tags, _, _, _ = load_game_data()
    
    print(f"开始批量跑测 - 模型数: {len(model_options)}, 地点数: {len(place_options)}, NPC数: {len(npc_options)}")
    print(f"总组合数: {len(model_options) * len(place_options) * len(npc_options)}")
    
    success_count = 0
    failure_count = 0
    
    # 遍历所有可能的组合
    for model in model_options:
        for place in place_options:
            for npc_index in npc_options:
                # 执行单个组合的跑测
                result_file, success = await offline_test_combination(
                    model, place, npc_index, npcs, tags
                )
                
                if success:
                    success_count += 1
                else:
                    failure_count += 1
    
    print(f"\n==== 跑测完成 ====")
    print(f"成功组合数: {success_count}")
    print(f"失败组合数: {failure_count}")
    print(f"日志保存在: offline_logs/ 目录下")

if __name__ == "__main__":
    # 创建logs目录（如果不存在）
    if not os.path.exists("offline_logs"):
        os.makedirs("offline_logs")
    
    # 运行所有组合的离线测试
    asyncio.run(main())