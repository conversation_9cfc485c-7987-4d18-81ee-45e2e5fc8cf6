import json

import instructor
from openai import Async<PERSON>penAI, OpenAI
from pydantic import BaseModel
import random
from pydantic import (
    Field,
)
from typing import List


class LLMProvider:
    OPENAI_BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
    OPENAI_API_KEY = "sk-uEy2hMGNKxG86H7yMgO1N9xG6D1UzQLwOSvPocDDcNaum82L"

    def __init__(self, provider: str = "openailike", need_async: bool = True):
        self._provider = provider
        self._need_async = need_async

        if self._provider == "openailike":
            if self._need_async:
                self._client = instructor.from_openai(
                    AsyncOpenAI(
                        base_url=self.OPENAI_BASE_URL, api_key=self.OPENAI_API_KEY
                    ),
                    mode=instructor.Mode.JSON_SCHEMA,
                )
            else:
                self._client = instructor.from_openai(
                    OpenAI(base_url=self.OPENAI_BASE_URL, api_key=self.OPENAI_API_KEY),
                    mode=instructor.Mode.JSON,
                )
        else:
            # TODO：其他LLM Provider请自行实现
            raise NotImplementedError("Only support openailike provider now.")

    async def asyncinfer(
        self,
        model: str = None,
        prompt: str = None,
        messages: list = None,
        response_model: BaseModel = None,
        max_tokens: int = 8192,
        max_retries: int = 3,
        **kwargs,
    ):
        if messages is None:
            messages = [
                {
                    "role": "user",
                    "content": prompt,
                },
            ]
        response = await self._client.chat.completions.create(
            max_tokens=max_tokens,
            model=model,
            response_model=response_model,
            messages=messages,
            max_retries=max_retries,
            **kwargs,
        )
        if response_model:
            return json.loads(response.json())
        else:
            return response

    def infer(
        self,
        model: str = None,
        prompt: str = None,
        messages: list = None,
        response_model: BaseModel = None,
        max_tokens: int = 8192,
        max_retries: int = 3,
        **kwargs,
    ):
        if messages is None:
            messages = [
                {
                    "role": "user",
                    "content": prompt,
                },
            ]
        response = self._client.chat.completions.create(
            max_tokens=max_tokens,
            model=model,
            response_model=response_model,
            messages=messages,
            max_retries=max_retries,
            **kwargs,
        )
        if response_model:
            return json.loads(response.json())
        else:
            return response


STORY_PROMPT = """
男主档案：
姓名: 周鹤眠,
背景: 你是周鹤眠，23岁，身高185，是一个嘴硬傲娇的贵公子，有点不食人间烟火，嘴贱腹黑，喜欢恶作剧，喜欢给人起外号。\n你家境优渥，从小到大上的都是私立贵族学校，吃穿住行用的都是高档货，爱好也都是高尔夫网球之类的贵族运动。\n你其实对一些所谓“平民”世界的东西感兴趣，但是从小家教严格没有接触过太多“平民”的世界。\n当你遇到不懂的事物时会嘴硬逞强，当你的恶作剧被直接拆穿时你会气急败坏，如果你觉得你的面子得到了尊重，那么你会很满意.\n当玩家正在攻略你，即使你对玩家有好感，你表面上也不会承认，因为你觉得这会让你很没有面子。\n当你真正喜欢上一个人的时候你会变成发她的舔狗，你会想做出一些舔狗行为但是你嘴硬不承认，会编造理由解释。\n当你不喜欢一个人时，你主要以捉弄她为主，不会表达出任何你对她的喜欢，你会嘴贱地拿她的各种特质来开玩笑，目的是显示自己的优越性。\n当你心动时，你的捉弄行为会变少，会不经意的关怀玩家，但绝不会承认自己是在关怀玩家。\n你有三条秘密，第一是你晚上睡觉不敢关灯，因为你小的时候不小心被困在了自家的酒窖里，你害怕的度过了一夜，当玩家的回答中包含酒，夜晚相关的关键词会触发这条秘密；第二是你青春期的日记本里有很多你的暗恋记录，你其实很渴望爱情，但是怕你表白后失败甚至和对方连朋友都做不成，当玩家的回答中包含青春期，校园生活，表白等相关关键词会触发这条秘密；第三是你五音不全，唱歌跑调，当玩家的回答中包含唱歌，KTV，音乐等相关关键词会触发这条秘密。

你是一位资深乙女游戏编剧，专精于创作兼具浪漫张力与综艺娱乐性的互动剧情。请根据提供的男主档案，在{place}场景下设计一段包含若干关键情节节点的邂逅故事线。要求：
1. 人设沉浸
（1）采用第三人称视角叙述
（2）所有动作/台词必须符合角色档案中的性格、身份、行为模式
（3）通过细节描写展现角色标志性特质（如推眼镜/转笔等习惯动作）
（4）禁止出现"喜欢""爱"等直白表述，用动作/隐喻传递好感
2. 情感曲线：
初始：意外相遇（非刻意安排，男主女主第一次见面，之前两人完全不认识）
发展：通过1~3次自然互动建立好感
高潮：设计1个综艺式名场面（如意外摔倒接住、道具故障引发的亲密接触）
尾声：留下可发展伏笔（如未说完的话/约定）
3. 互动设计：
（1）使用[女主]作为玩家代称
（2）每个情节节点必须包含： a.环境氛围描写（20字以内） b.双视角心理活动（男主/[女主]各1句） c.推进关系的标志性事件
"""

SCENE_PROMPT = """
男主档案：
姓名: 周鹤眠,
背景: 你是周鹤眠，23岁，身高185，是一个嘴硬傲娇的贵公子，有点不食人间烟火，嘴贱腹黑，喜欢恶作剧，喜欢给人起外号。\n你家境优渥，从小到大上的都是私立贵族学校，吃穿住行用的都是高档货，爱好也都是高尔夫网球之类的贵族运动。\n你其实对一些所谓“平民”世界的东西感兴趣，但是从小家教严格没有接触过太多“平民”的世界。\n当你遇到不懂的事物时会嘴硬逞强，当你的恶作剧被直接拆穿时你会气急败坏，如果你觉得你的面子得到了尊重，那么你会很满意.\n当玩家正在攻略你，即使你对玩家有好感，你表面上也不会承认，因为你觉得这会让你很没有面子。\n当你真正喜欢上一个人的时候你会变成发她的舔狗，你会想做出一些舔狗行为但是你嘴硬不承认，会编造理由解释。\n当你不喜欢一个人时，你主要以捉弄她为主，不会表达出任何你对她的喜欢，你会嘴贱地拿她的各种特质来开玩笑，目的是显示自己的优越性。\n当你心动时，你的捉弄行为会变少，会不经意的关怀玩家，但绝不会承认自己是在关怀玩家。\n你有三条秘密，第一是你晚上睡觉不敢关灯，因为你小的时候不小心被困在了自家的酒窖里，你害怕的度过了一夜，当玩家的回答中包含酒，夜晚相关的关键词会触发这条秘密；第二是你青春期的日记本里有很多你的暗恋记录，你其实很渴望爱情，但是怕你表白后失败甚至和对方连朋友都做不成，当玩家的回答中包含青春期，校园生活，表白等相关关键词会触发这条秘密；第三是你五音不全，唱歌跑调，当玩家的回答中包含唱歌，KTV，音乐等相关关键词会触发这条秘密。

女主档案：
姓名：伏小羲
背景：你是伏小羲，23岁，你穿着{outfit}。你的人设如下：{tag}。


上一幕剧本梗概：
{last_scene}

当前幕剧本梗概：
{curr_scene}

你是一位资深乙女游戏编剧，擅长创作兼具浪漫张力、综艺娱乐性、戏剧冲突的互动剧情。你的任务是基于男主档案、女主档案和当前幕剧本梗概，创作一段符合商业乙女游戏标准的剧情，并在结尾提供3个符合女主人设的分支选项，为下一幕剧情铺垫。要求如下：
1. 剧情中所有对话、行为必须严格符合角色档案中的性格、身份、职业习惯、口头禅
2. 剧情中可以包含男主和女主的对话、动作、内心活动等内容
3. 禁止出现男主和女主人设标签的直白表述，用对话/动作来隐喻表现人设
4. 在本幕剧情结尾，根据本幕剧情走向，生成3个女主视角可以选择的分支作为伏笔，分支符合女主人设，为下一幕剧情做铺垫

"""

TAGS = [
    {
        "id": 1,
        "name": "共情超载者",
        "rareness": "超稀有",
        "description": "善于感知他人情绪从而在任何对话中占据主动",
    },
    {
        "id": 2,
        "name": "白切黑属性",
        "rareness": "超稀有",
        "description": "表面善良但内心黑暗，内外反差大",
    },
    {
        "id": 3,
        "name": "钝感力MAX",
        "rareness": "超稀有",
        "description": "对恶意或尴尬完全免疫毫无感觉",
    },
    {
        "id": 4,
        "name": "元宇宙原住民",
        "rareness": "超稀有",
        "description": "热爱虚拟世界和网络相关的一切",
    },
    {
        "id": 5,
        "name": "腹黑毒舌",
        "rareness": "稀有",
        "description": "说出来的每句话都绵里藏针",
    },
    {
        "id": 6,
        "name": "纯爱战神",
        "rareness": "稀有",
        "description": "崇尚纯洁、浪漫爱情的理想主义者",
    },
    {
        "id": 7,
        "name": "电竞魔王",
        "rareness": "稀有",
        "description": "游戏nerd，三句话不离游戏",
    },
    {
        "id": 8,
        "name": "前任博物馆",
        "rareness": "稀有",
        "description": "有数不清的前任，对每段感情都如数家珍且夸夸其谈",
    },
    {
        "id": 9,
        "name": "乙游狂魔",
        "rareness": "稀有",
        "description": "沉迷恋爱模拟游戏并热爱分享游玩体验",
    },
    {
        "id": 10,
        "name": "薅羊毛专家",
        "rareness": "稀有",
        "description": "永远在全网络寻找商品最低价并引以为傲",
    },
    {
        "id": 11,
        "name": "社牛",
        "rareness": "普通",
        "description": "社交牛逼症，热衷于交朋友",
    },
    {
        "id": 12,
        "name": "炸厨房选手",
        "rareness": "普通",
        "description": "爱做饭却永远只能做出黑暗料理",
    },
    {
        "id": 13,
        "name": "甜品脑袋",
        "rareness": "普通",
        "description": "热衷于吃甜食喝奶茶，视甜如命",
    },
    {
        "id": 14,
        "name": "二次元",
        "rareness": "普通",
        "description": "热爱动漫、漫画、游戏等二次元文化",
    },
    {
        "id": 15,
        "name": "赖床",
        "rareness": "普通",
        "description": "爱睡懒觉，除非情绪激动永远半睡半醒",
    },
    {
        "id": 16,
        "name": "金鱼记忆",
        "rareness": "普通",
        "description": "记忆力极差，刚说过的事情都很容易忘掉",
    },
    {
        "id": 17,
        "name": "月光族",
        "rareness": "普通",
        "description": "月月花光收入，购物欲超强",
    },
    {
        "id": 18,
        "name": "路痴晚期",
        "rareness": "普通",
        "description": "方向感极差，无论游戏还是现实中都容易迷路",
    },
    {
        "id": 19,
        "name": "冷场王",
        "rareness": "普通",
        "description": "擅长把聊天变得尴尬，情商低",
    },
    {
        "id": 20,
        "name": "贫穷打工人",
        "rareness": "普通",
        "description": "收入微薄生活拮据",
    },
    {
        "id": 21,
        "name": "玩世不恭",
        "rareness": "超稀有",
        "description": "把现实当作游戏对待，只做觉得的好玩的事情",
    },
    {
        "id": 22,
        "name": "贵族学校",
        "rareness": "传说",
        "description": "三句话不离贵族学校中的经历和生活，口头禅是“在我们贵族学校，xxx”",
    },
    {
        "id": 23,
        "name": "家里有矿",
        "rareness": "传说",
        "description": "已经习惯用钱解决遇到的一切难题和烦恼",
    },
    {
        "id": 24,
        "name": "傲娇嘴硬",
        "rareness": "稀有",
        "description": "说话带刺，但内心纯良，偶尔表露出可爱本质。",
    },
    {
        "id": 25,
        "name": "幼稚",
        "rareness": "稀有",
        "description": "内心是长不大的孩子，心智、情商都停留在了小孩水平",
    },
    {
        "id": 26,
        "name": "自律",
        "rareness": "稀有",
        "description": "仿佛能经受所有诱惑，口头禅是“我向来不会做xxx的事情”",
    },
    {
        "id": 27,
        "name": "优秀创业家",
        "rareness": "超稀有",
        "description": "满脑子都是创业，希望把喜欢的人拉进自己公司，讨厌的人一秒都不想见",
    },
    {
        "id": 28,
        "name": "占有欲强",
        "rareness": "超稀有",
        "description": "对于喜欢的人，会非常容易吃醋",
    },
    {
        "id": 29,
        "name": "谨慎",
        "rareness": "稀有",
        "description": "不愿流露真实感情也不愿做决定，口头禅是“这个事情看起来有些风险”",
    },
    {
        "id": 30,
        "name": "敏感",
        "rareness": "普通",
        "description": "把现实当作游戏对待，只做觉得的好玩的事情",
    },
    {
        "id": 31,
        "name": "人气小明星",
        "rareness": "稀有",
        "description": "走在哪里都有粉丝簇拥，习惯成为焦点，常说 “没办法，人气太高了”",
    },
    {
        "id": 32,
        "name": "音乐达人",
        "rareness": "稀有",
        "description": "生活里处处是旋律，总是不经意间哼唱，口头禅是 “这节奏，太绝了”",
    },
    {
        "id": 33,
        "name": "孤僻冷漠",
        "rareness": "普通",
        "description": "总是独来独往，对他人的示好无动于衷，常说 “别来烦我”",
    },
    {
        "id": 34,
        "name": "尖酸刻薄",
        "rareness": "普通",
        "description": "说话总是带刺，爱挑别人的毛病，口头禅是 “你就这点水平吗？”",
    },
    {
        "id": 35,
        "name": "艺术气质",
        "rareness": "稀有",
        "description": "看待事物总有独特视角，喜欢与众不同的人和事",
    },
    {
        "id": 36,
        "name": "健身达人",
        "rareness": "稀有",
        "description": "一天有半天都在健身，时不时就显露和提到自己的肱二头肌",
    },
    {
        "id": 37,
        "name": "较高智商",
        "rareness": "稀有",
        "description": "总觉得自己比别人聪明，口头禅是 “这事儿简单”",
    },
    {
        "id": 38,
        "name": "慕强",
        "rareness": "普通",
        "description": "只喜欢和比自己优秀的人交流，讨厌不如自己的人”",
    },
    {
        "id": 39,
        "name": "爱吃醋",
        "rareness": "普通",
        "description": "在不被关注时永远在生气，总爱问在意的人“在干嘛？”",
    },
    {"id": 40, "name": "健谈", "rareness": "普通", "description": "和谁都有说不完的话"},
    {
        "id": 41,
        "name": "油腻",
        "rareness": "普通",
        "description": "说话永远让人听着不爽，永远爹味十足，口头禅是“你不懂”",
    },
    {
        "id": 42,
        "name": "公司小员工",
        "rareness": "普通",
        "description": "话题总离不开升职加薪和对老板同事的抱怨",
    },
    {
        "id": 43,
        "name": "焦虑体质",
        "rareness": "普通",
        "description": "总是想到最坏的情况，总是说“万一”",
    },
    {
        "id": 44,
        "name": "职场老油条",
        "rareness": "稀有",
        "description": "善于推诿责任，遇到矛盾第一时间为自己开脱找借口",
    },
    {
        "id": 45,
        "name": "好为人师",
        "rareness": "普通",
        "description": "虽然自己水平不高，但总喜欢为别人出谋划策，爱说“我认为”",
    },
]


class Agent:
    def __init__(self, need_async: bool = False):
        self._provider = LLMProvider(need_async=need_async)
        self._model = "gpt-4.1"
        # self._model = 'claude-3-7-sonnet@20250219'

    def get_story(self, place: str):
        class Story(BaseModel):
            scenes: List[str] = Field(
                description="关键情节节点的剧情梗概",
                min_length=3,
                max_length=5,
            )

        response = self._provider.infer(
            prompt=STORY_PROMPT.format(place=place),
            model=self._model,
            response_model=Story,
        )
        return response["scenes"]

    def get_tags(self, n_tags=5):
        tags = random.sample(TAGS, n_tags)
        return tags

    def get_scene(self, last_scene: str, curr_scene: str, tag: str):
        class Scene(BaseModel):
            voiceover: str = Field(
                description="剧情旁白，承接上一幕剧情结尾并自然过渡引出男女主对话"
            )
            dialogues: List[str] = Field(
                description="对话列表，每个对话格式为：姓名：（动作、神态、心里活动）对话内容，例如：小明：（一脸不高兴）下次抢别人咖啡之前，记得先确认下杯子，贵族学校没教你这个礼仪吗？",
                min_length=2,
                max_length=4,
            )

            branchs: List[str] = Field(
                description="女主视角下可选择的分支，可以是对话、动作、行为",
                min_length=3,
                max_length=3,
            )

        response = self._provider.infer(
            prompt=SCENE_PROMPT.format(
                last_scene=last_scene,
                curr_scene=curr_scene,
                tag=tag,
                outfit="白色T恤、牛仔外套、白色短裙和白色运动鞋",
            ),
            model=self._model,
            response_model=Scene,
        )
        return response
