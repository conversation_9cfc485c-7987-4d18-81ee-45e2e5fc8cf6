import streamlit as st

from agent import Agent


def main():
    if "agent" not in st.session_state:
        st.session_state.agent = Agent()
        st.session_state.story = None
        st.session_state.scene = None
        st.session_state.curr_idx = 0
        st.session_state.last_idx = -1
        st.session_state.run = True
        st.session_state.history = []
        st.session_state.tags = None
        st.session_state.tags_str = None
        st.session_state.branch = ""

    if st.button("随机标签"):
        st.session_state.tags = st.session_state.agent.get_tags()

    if st.session_state.tags:
        st.write(st.session_state.tags)
        st.session_state.tags_str = "；".join(
            [tag["name"] + "：" + tag["description"] for tag in st.session_state.tags]
        )

    inputs = st.text_input(
        label="输入攻略场景：",
        value="",
        max_chars=100,
        key="input_place",
    )

    if st.button("生成故事线"):
        st.session_state.story = st.session_state.agent.get_story(place=inputs)

    if st.session_state.story:
        st.write("---")
        st.write(st.session_state.story)
        st.write("---")

        if st.session_state.curr_idx >= len(st.session_state.story):
            st.error("攻略完成...")
            st.write(st.session_state.history)
        else:
            if st.session_state.run:
                curr_scene = st.session_state.story[st.session_state.curr_idx]
                last_scene = (
                    st.session_state.story[st.session_state.last_idx]
                    if st.session_state.last_idx != -1
                    else "无"
                )
                st.session_state.scene = st.session_state.agent.get_scene(
                    curr_scene=curr_scene,
                    last_scene=last_scene + st.session_state.branch,
                    tag=st.session_state.tags_str,
                )
                st.session_state.history.append(st.session_state.scene)
                st.session_state.run = False

            st.warning(st.session_state.scene["voiceover"])
            st.write(st.session_state.scene["dialogues"])

            branchs = st.radio(
                "请做出选择：",
                options=list(st.session_state.scene["branchs"]),
                index=None,
                key="selected_branchs",
            )

            if st.button("确认选择"):
                st.session_state.branch = branchs
                st.session_state.history.append(branchs)
                st.session_state.last_idx = st.session_state.curr_idx
                st.session_state.curr_idx += 1
                st.session_state.run = True
                st.rerun()


if __name__ == "__main__":
    main()
