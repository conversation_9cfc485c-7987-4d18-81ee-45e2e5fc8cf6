import streamlit as st
import os
from code.config import Config
from code.utils import load_data, roll_random_outfits, save_data
from code.agent import generate_decision, generate_story, generate_thrill
import asyncio
import time
import json
import pandas as pd

# 设置页面配置
st.set_page_config(
    page_title="异世界魔女降临地球",
    page_icon="💖",
    layout="wide"
)

# 加载数据
@st.cache_data
def load_game_data():
    # 加载地点
    place_xlsx = pd.read_excel("data/places.xlsx", engine='openpyxl', na_filter=False, keep_default_na=False)
    places = {}
    for i, place in place_xlsx.iterrows():
        places[place['name']] = {
            'id': i,
            'name': str(place['name']),
            'description_model': str(place['description_model'])
        }
    # 加载技能
    skill_xlsx = pd.read_excel("data/skills.xlsx", engine='openpyxl', na_filter=False, keep_default_na=False)
    skills = []
    for i, skill in skill_xlsx.iterrows():
        skills.append({
            'id': i,
            'name': str(skill['name']),
            'description_usr': str(skill['description_player']),
            'description': str(skill['description_model'])
        })
    # 加载npc
    npc_xlsx = pd.read_excel("data/npcs.xlsx", engine='openpyxl', na_filter=False, keep_default_na=False)
    npcs = []
    for i, npc in npc_xlsx.iterrows():
        npcs.append({
            'id': i,
            'name': str(npc['name']),
            'shortbio': str(npc['shortbio']),
            'bio': str(npc['bio']),
            'pre_story': json.loads(str(npc['pre_story']))
        })
    # 加载prompt
    prompt_xlsx = pd.read_excel("data/prompt.xlsx", engine='openpyxl', na_filter=False, keep_default_na=False)
    prompts = {}
    for _, p in prompt_xlsx.iterrows():
        prompts[p['name']] = p['content']
    
    # 加载可选装扮与玩家信息
    outfits = load_data("data/outfits.json")
    players = load_data("data/players.json")
    
    # 确保玩家数据存在
    player = players[0] if players else {"id": '0', "name": "玩家", "bio": "一位神秘的旅行者"}
    
    return skills, npcs, outfits, player, prompts, places

skills, npcs, outfits_data, player, prompts, places = load_game_data()

# 初始化会话状态
if 'game_state' not in st.session_state:
    st.session_state.game_state = 'intro'  # intro, npc_selection, tag_selection, outfit_selection, background, decision_count, history, dialogue, end
    st.session_state.selected_npc = None
    st.session_state.player_outfits = {}
    st.session_state.outfit_rolls = 0
    st.session_state.current_outfits = {}
    st.session_state.max_decisions = 0
    st.session_state.dialogue_history = []
    st.session_state.current_dialogue = ""
    st.session_state.current_choices = []
    st.session_state.player = player
    st.session_state.chat_rounds = 0
    st.session_state.skills = skills
    st.session_state.prompts = prompts
    st.session_state.npcs = npcs
    st.session_state.outfits_data = outfits_data
    st.session_state.places = places

# 游戏流程控制
async def main():
    st.title("异世界魔女降临地球")
    
    # 游戏介绍
    if st.session_state.game_state == 'intro':
        st.markdown(f"""
        ### 游戏介绍
        欢迎来到异世界魔女降临地球！
        """)
        
        if st.button("开始游戏"):
            st.session_state.game_state = 'global_setting'
            st.rerun()
    
    # 可自定义全局变量
    elif st.session_state.game_state == 'global_setting':
        # 上传地点配置
        place_file = st.file_uploader("上传地点配置表", type=["xlsx"])
        if place_file is not None:
            place_xlsx = pd.read_excel(place_file, engine='openpyxl', na_filter=False, keep_default_na=False)
            places = {}
            for i, place in place_xlsx.iterrows():
                places[place['name']] = {
                    'id': i,
                    'name': str(place['name']),
                    'description_model': str(place['description_model'])
                }
            st.session_state.places = places
        # 上传npc配置表
        npc_file = st.file_uploader("上传NPC配置表", type=["xlsx"])
        if npc_file is not None:
            npc_xlsx = pd.read_excel(npc_file, engine='openpyxl', na_filter=False, keep_default_na=False)
            npcs = []
            for i, npc in npc_xlsx.iterrows():
                npcs.append({
                    'id': i,
                    'name': str(npc['name']),
                    'shortbio': str(npc['shortbio']),
                    'bio': str(npc['bio']),
                    'pre_story': json.loads(str(npc['pre_story']))
                })
            st.session_state.npcs = npcs
        # 上传skills配置表
        skill_file = st.file_uploader("上传技能配置表", type=["xlsx"])
        if skill_file is not None:
            skill_xlsx = pd.read_excel(skill_file, engine='openpyxl', na_filter=False, keep_default_na=False)
            skills = []
            for i, skill in skill_xlsx.iterrows():
                skills.append({
                    'id': i,
                    'name': str(skill['name']),
                    'description_usr': str(skill['description_player']),
                    'description': str(skill['description_model'])
                })
            st.session_state.skills = skills
        # 上传prompt配置
        prompt_file = st.file_uploader("上传prompt", type=["xlsx"])
        if prompt_file is not None:
            prompt_xlsx = pd.read_excel(prompt_file, engine='openpyxl', na_filter=False, keep_default_na=False)
            prompts = {}
            for _, p in prompt_xlsx.iterrows():
                prompts[p['name']] = p['content']
            st.session_state.prompts = prompts
        # 互动次数
        st.write("请设置你希望与NPC进行互动的次数（2-8次）")
        decision_count = st.number_input(
            "互动次数",
            min_value=2,      # 最小值
            max_value=20,      # 最大值
            value=5,          # 默认值
            step=1,           # 步长
            help="选择2-20之间的整数，表示你希望与NPC进行互动的次数"
        )
        
        # 互动模式
        st.write("请选择你想要的互动节奏")
        mode = st.selectbox(
            "选择模式",
            options=["快节奏", "慢节奏"],
            index=0,
            help="快节奏剧情不包括心理动作描写，只有对话，长度更短"
        )

        # 模型设定
        st.write("请选择用于生成对话的AI模型")
        available_models = [
            'gpt-4.1', 
            'gpt-4.5-preview', 
            'deepseek-v3-250324', 
            'claude-3-7-sonnet@20250219',
            'claude-sonnet-4@20250514',
            'claude-opus-4@20250514',
            'gemini-2.0-flash-001',
            'gemini-2.5-pro-preview-05-06',
            'gemini-2.0-flash-thinking-exp-01-21',
            'gemini-1.5-pro-001',
            'Doubao-1.5-pro-32k-character-250228', 
            'Doubao-Seed-1.6-thinking-250615',
        ]
        # 创建下拉选择框（带默认值）
        selected_model = st.selectbox(
            "选择模型",
            options=available_models,
            index=available_models.index('gpt-4.1'),  # 默认选中gpt-4.1
            help="选择用于生成对话的模型"
        )

        # 设置游戏中名字
        st.write("请输入你的游戏名称")
        player_name = st.text_input(
            "请输入你的游戏名称",
            value='伏小羲',
            max_chars=10,
            help="输入1-10个字符作为你的游戏名称"
        )
        if len(player_name) > 10:
            player_name = player_name[:10]
        
        # 设置地点或自定义地点
        st.write("请选择剧情发生的地点")
        place = st.selectbox(
            "选择地点",
            options=list(st.session_state.places.keys()),
            index=0,
            help=""
        )
        st.write("如需自定义，请输入地点和描述")
        diy_place = st.text_input(
            "请输入你的地点名称",
            value='',
            max_chars=10,
            help=""
        )
        diy_place_desc = st.text_area(
            label="请输入地点描述（可选）",
            value='',
            height=200,
            max_chars=1000,
            help="",
            key='place_desc_diy',
        )
        
        if st.button("确认设置"):
            st.session_state.max_decisions = decision_count
            st.session_state.chat_model = selected_model
            st.session_state.game_state = 'npc_selection'
            st.session_state.player['name'] = player_name
            if diy_place == '':
                st.session_state.place = st.session_state.places[place]
            else:
                st.session_state.place = {"name": diy_place, "description_model": diy_place_desc}
            st.session_state.story_gen_sys = Config.story_gen_default_sys if mode == "慢节奏" else st.session_state.prompts['story_gen_default_sys_short']
            st.session_state.choice_gen_sys = Config.choice_gen_default_sys if mode == "慢节奏" else st.session_state.prompts['choice_gen_default_sys_short']
            st.session_state.thrill_gen_sys = st.session_state.prompts['thrill_gen_default_sys']
            st.rerun()
    
    # 选择NPC
    elif st.session_state.game_state == 'npc_selection':
        st.header("选择你的心动对象")
        
        for i, npc in enumerate(st.session_state.npcs):
            with st.container():
                st.subheader(npc['name'])
                
                # 使用两列布局展示NPC信息
                col1, col2 = st.columns([1, 3])
                
                # 左侧放头像占位图
                with col1:
                    st.image(f"https://picsum.photos/seed/npc{i}/200/200", use_container_width=True)
                
                # 右侧放简介和标签
                with col2:
                    st.markdown(f"**简介:**\n\n{npc['shortbio']}")
                
                # 选择按钮
                if st.button(f"选择 {npc['name']}", key=f"select_npc_{i}"):
                    st.session_state.selected_npc = npc
                    st.session_state.game_state = 'skill_choose'
                    st.rerun()
        
        # 添加分隔线
        st.markdown("---")
    
    # 在给定技能里选择一个作为初始技能
    elif st.session_state.game_state == 'skill_choose':
        st.header("选择你的技能")
        st.write("从技能库中选一个技能")

        selected_skill = None
        col1, col2 = st.columns(2)
        
        # 技能选择区域
        with st.container():
            st.subheader("可用技能")
            for i, skill in enumerate(st.session_state.skills):
                with (col1 if i % 2 == 0 else col2):
                    with st.expander(f"**{skill['name']}**"):
                        st.write(skill['description'])
                        if st.button(f"选择 {skill['name']}", key=f"skill_{skill['id']}"):
                            selected_skill = skill
        
        # 暂不选择按钮（独立区域，突出显示）
        st.markdown("---")  # 分隔线
        with st.container():
            if st.button("暂不选择技能，稍后再选", key="postpone_skill_choice", 
                        help="暂时不选择技能，可在后续剧情中使用"):
                st.session_state.selected_skill = None  # 记录未选择技能
                st.session_state.game_state = 'intro_generation'
                st.rerun()
        
        # 处理技能选择
        if selected_skill:
            st.session_state.selected_skill = selected_skill
            # 进入下一步
            st.session_state.game_state = 'intro_generation'
            st.rerun()
    
    # 根据地点、技能、引子生成剧情
    elif st.session_state.game_state == 'intro_generation':
        # 检查是否已经生成过剧情
        if 'intro' not in st.session_state:
            # 将引子添加到对话历史
            st.session_state.intro = "".join(st.session_state.selected_npc['pre_story'])
            if not any("voiceover" in msg for msg in st.session_state.dialogue_history):
                st.session_state.dialogue_history.append({
                    "role":"voiceover", "content":st.session_state.intro
                })
            # 生成首次剧情
            with st.spinner("AI正在生成首段剧情..."):
                first_scene = await generate_story(
                    st.session_state.selected_npc,
                    st.session_state.player,
                    st.session_state.place,
                    st.session_state.player_outfits,
                    st.session_state.dialogue_history,
                    st.session_state.chat_model,
                    st.session_state.selected_skill,
                    st.session_state.story_gen_sys,
                )
            
            # 添加到对话历史
            st.session_state.dialogue_history.extend(first_scene)
            st.session_state.game_state = 'outfit_selection'
            st.rerun()
    
    # 选择装扮
    elif st.session_state.game_state == 'outfit_selection':
        st.header("选择你的装扮")
        st.write(f"你还有 {Config.MAX_OUTFIT_ROLLS - st.session_state.outfit_rolls} 次抽取机会")
        
        # 抽取按钮
        if st.button("抽取装扮", disabled=st.session_state.outfit_rolls > Config.MAX_OUTFIT_ROLLS):
            # 为每个部位抽取一个装扮
            st.session_state.current_outfits = roll_random_outfits(st.session_state.outfits_data, Config.OUTFITS_PER_ROLL)
            st.session_state.outfit_rolls += 1
        
        # 显示当前抽取的装扮
        if st.session_state.current_outfits:
            st.subheader("本轮抽取的装扮")
            for part, outfit in st.session_state.current_outfits.items():
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.write(f"**{part}**: {outfit['name']} - {outfit['description']}")
            
            # 确认或重新抽取
            col1, col2 = st.columns(2)
            with col1:
                if st.button("接受当前装扮", disabled=st.session_state.outfit_rolls > Config.MAX_OUTFIT_ROLLS):
                    st.session_state.player_outfits = st.session_state.current_outfits
                    st.session_state.game_state = 'background'
                    st.rerun()
            with col2:
                if st.button("重新抽取", disabled=st.session_state.outfit_rolls >= Config.MAX_OUTFIT_ROLLS):
                    # 清空当前选择，重新抽取
                    st.session_state.current_outfits = {}
                    st.rerun()

    # 显示背景故事
    elif st.session_state.game_state == 'background':
        st.header(f"引子")
        st.markdown(st.session_state.intro)

        # AI根据场景、NPC风格，对装扮进行打分，确定装扮初始心动值
        with st.spinner("AI正在计算装扮心动值..."):
            thrill, reason = await generate_thrill(
                st.session_state.selected_npc,
                st.session_state.player,
                st.session_state.place,
                st.session_state.player_outfits,
                st.session_state.chat_model,
                st.session_state.thrill_gen_sys,
            )
        st.header("装扮心动值及理由")
        st.markdown(f"心动值为：{thrill}，理由为：{reason}")
        
        if st.button("继续"):
            st.session_state.game_state = 'decision_count'
            st.rerun()
    
    # 确定决策次数
    elif st.session_state.game_state == 'decision_count':
        # 读取之前的global setting
        st.header(f"你们将有 {st.session_state.max_decisions} 次互动机会")
        st.write("每次互动都会影响你们之间的关系，谨慎选择！")
        
        if st.button("开始对话"):
            st.session_state.game_state = 'history'
            st.rerun()
    
    # 显示历史对话
    elif st.session_state.game_state == 'history':
        for message in st.session_state.dialogue_history:
            if message['role'] == 'npc':
                st.info(f"**{st.session_state.selected_npc['name']}**: {message['content']}")
            elif message['role'] == 'player':
                st.warning(f"**{st.session_state.player['name']}**: {message['content']}")
            elif message['role'] == 'voiceover':
                st.markdown(f"**旁白**: {message['content']}")
        
        if st.button("继续"):
            # 生成初始对话
            with st.spinner("AI正在生成对话..."):
                dialogue, choices = await generate_decision(
                    st.session_state.selected_npc,
                    st.session_state.player,
                    st.session_state.player_outfits,
                    st.session_state.dialogue_history,
                    st.session_state.chat_model,
                    st.session_state.selected_skill,
                    st.session_state.choice_gen_sys,
                )
            
            st.session_state.current_dialogue = dialogue
            st.session_state.current_choices = choices
            st.session_state.game_state = 'dialogue'
            st.session_state.dialogue_history.append(dialogue)
            st.rerun()
    
    # 对话环节
    elif st.session_state.game_state == 'dialogue':
        st.header(f"对局信息")
        st.markdown(f"玩家装扮：{', '.join([o['name'] for o in st.session_state.current_outfits.values()])}")
        st.header(f"正在攻略： {st.session_state.selected_npc['name']}")
        
        # 显示历史对话
        st.markdown(st.session_state.intro + '\n')
        for message in st.session_state.dialogue_history:
            if message['role'] == 'npc':
                st.info(f"**{st.session_state.selected_npc['name']}**: {message['content']}")
            elif message['role'] == 'player':
                st.warning(f"**{st.session_state.player['name']}**: {message['content']}")
            elif message['role'] == 'voiceover':
                st.markdown(f"**旁白**: {message['content']}")
            elif message['role'] == 'skill':
                st.markdown(f"**技能**: {message['content']}")
        
        # 显示当前对话
        st.subheader("面对问题和决策，她将如何进行选择？")
        if st.session_state.current_dialogue['role'] == 'npc':
            tmp_role = st.session_state.selected_npc['name']
        elif st.session_state.current_dialogue['role'] == 'voiceover':
            tmp_role = "旁白"
        elif st.session_state.current_dialogue['role'] == 'player':
            tmp_role = st.session_state.player['name']
        else:
            tmp_role = st.session_state.current_dialogue['role']
        st.markdown(f"**{tmp_role}**: {st.session_state.current_dialogue['content']}\n")
        
        # 显示选项
        col1, col2, col3 = st.columns(3)
        
        for i, choice in enumerate(st.session_state.current_choices):
            if i == 0:
                col = col1
            elif i == 1:
                col = col2
            else:
                col = col3
            
            with col:
                # 使用markdown显示选项文本（支持复制）
                st.markdown(f"**选项 {i+1}:** {choice['answer']}")
                
                # 使用更小的按钮用于选择
                if st.button(f"选择选项 {i+1}", key=f"select_choice_{i}"):
                    # 记录玩家选择
                    st.session_state.dialogue_history.append({"role":"player", "content":choice['answer']})
                    st.session_state.chat_rounds += 1
                    st.session_state.dialogue_history.extend(choice['reaction'])
                    
                    # 检查是否达到决策次数上限
                    if st.session_state.chat_rounds >= st.session_state.max_decisions:
                        st.session_state.game_state = 'end'
                        st.rerun()
                    
                    # 如果有技能且不是最后一轮，进入技能选择状态
                    if st.session_state.skills and st.session_state.chat_rounds < st.session_state.max_decisions:
                        st.session_state.game_state = 'skill_usage'
                        st.rerun()
                    else:
                        # 没有技能或已到最后一轮，直接生成后续剧情，保持当前技能选择
                        # st.session_state.selected_skill = None
                        
                        # 生成后续对话
                        with st.spinner("AI正在生成剧情..."):
                            followup_dialogue = await generate_story(
                                st.session_state.selected_npc,
                                st.session_state.player,
                                st.session_state.place,
                                st.session_state.player_outfits,
                                st.session_state.dialogue_history,
                                st.session_state.chat_model,
                                st.session_state.selected_skill,
                                st.session_state.story_gen_sys,
                            )
                        
                        # 添加到对话历史
                        st.session_state.dialogue_history.extend(followup_dialogue)
                        
                        # 生成下一个决策点
                        with st.spinner("AI正在生成下一个问题..."):
                            next_dialogue, next_choices = await generate_decision(
                                st.session_state.selected_npc,
                                st.session_state.player,
                                st.session_state.player_outfits,
                                st.session_state.dialogue_history,
                                st.session_state.chat_model,
                                st.session_state.selected_skill,
                                st.session_state.choice_gen_sys,
                            )
                            st.session_state.dialogue_history.append(next_dialogue)
                        
                        st.session_state.current_dialogue = next_dialogue
                        st.session_state.current_choices = next_choices
                        st.rerun()

    # 技能使用环节
    elif st.session_state.game_state == 'skill_usage':
        st.header(f"对局信息")
        st.markdown(f"玩家装扮：{', '.join([o['name'] for o in st.session_state.current_outfits.values()])}")
        st.header(f"正在攻略： {st.session_state.selected_npc['name']}")
        
        # 显示历史对话（包括最新的选择和后续对话）
        st.markdown(st.session_state.intro + '\n')
        for message in st.session_state.dialogue_history:
            if message['role'] == 'npc':
                st.info(f"**{st.session_state.selected_npc['name']}**: {message['content']}")
            elif message['role'] == 'player':
                st.warning(f"**{st.session_state.player['name']}**: {message['content']}")
            elif message['role'] == 'voiceover':
                st.markdown(f"**旁白**: {message['content']}")
        
        st.subheader("使用技能")
        st.write("您可以使用一个技能来影响后续剧情")
        
        col1, col2 = st.columns(2)
        
        for i, skill in enumerate(st.session_state.skills):
            with (col1 if i % 2 == 0 else col2):
                with st.expander(f"**{skill['name']}**"):
                    st.write(skill['description'])
                    if st.button(f"使用 {skill['name']}", key=f"use_skill_{skill['id']}"):
                        st.session_state.selected_skill = skill
                        st.session_state.dialogue_history.append({
                            "role": "skill",
                            "content": f"你使用了技能 **{skill['name']}** - {skill['description']}"
                        })
                        
                        # 生成后续对话
                        with st.spinner("AI正在生成剧情..."):
                            followup_dialogue = await generate_story(
                                st.session_state.selected_npc,
                                st.session_state.player,
                                st.session_state.place,
                                st.session_state.player_outfits,
                                st.session_state.dialogue_history,
                                st.session_state.chat_model,
                                st.session_state.selected_skill,
                                st.session_state.story_gen_sys,
                            )
                        
                        # 添加到对话历史
                        st.session_state.dialogue_history.extend(followup_dialogue)
                        
                        # 生成下一个决策点
                        with st.spinner("AI正在生成下一个问题..."):
                            next_dialogue, next_choices = await generate_decision(
                                st.session_state.selected_npc,
                                st.session_state.player,
                                st.session_state.player_outfits,
                                st.session_state.dialogue_history,
                                st.session_state.chat_model,
                                st.session_state.selected_skill,
                                st.session_state.choice_gen_sys,
                            )
                            st.session_state.dialogue_history.append(next_dialogue)
                        
                        st.session_state.current_dialogue = next_dialogue
                        st.session_state.current_choices = next_choices
                        st.session_state.game_state = 'dialogue'  # 回到对话环节
                        st.rerun()
        
        if st.button("维持当前技能，继续剧情", key="skip_skill"):
            # st.session_state.selected_skill = None
            
            # 生成后续对话
            with st.spinner("AI正在生成剧情..."):
                followup_dialogue = await generate_story(
                    st.session_state.selected_npc,
                    st.session_state.player,
                    st.session_state.place,
                    st.session_state.player_outfits,
                    st.session_state.dialogue_history,
                    st.session_state.chat_model,
                    st.session_state.selected_skill,
                    st.session_state.story_gen_sys,
                )
            
            # 添加到对话历史
            st.session_state.dialogue_history.extend(followup_dialogue)
            
            # 生成下一个决策点
            with st.spinner("AI正在生成下一个问题..."):
                next_dialogue, next_choices = await generate_decision(
                    st.session_state.selected_npc,
                    st.session_state.player,
                    st.session_state.player_outfits,
                    st.session_state.dialogue_history,
                    st.session_state.chat_model,
                    st.session_state.selected_skill,
                    st.session_state.choice_gen_sys,
                )
                st.session_state.dialogue_history.append(next_dialogue)
            
            st.session_state.current_dialogue = next_dialogue
            st.session_state.current_choices = next_choices
            st.session_state.game_state = 'dialogue'  # 回到对话环节
            st.rerun()
    
    # 游戏结束
    elif st.session_state.game_state == 'end':
        # 显示完整对话历史
        st.subheader("对话记录")
        for message in st.session_state.dialogue_history:
            if message['role'] == 'npc':
                st.info(f"**{st.session_state.selected_npc['name']}**: {message['content']}")
            elif message['role'] == 'player':
                st.warning(f"**{st.session_state.player['name']}**: {message['content']}")
            elif message['role'] == 'voiceover':
                st.markdown(f"**旁白**: {message['content']}")
        
        st.header("攻略结束")
        st.write("请等待NPC的答复...")
        
        # 生成结局
        # with st.spinner("AI正在生成结局..."):
        #     # 这里使用generate_next_decision作为结局生成器，实际应用中可以创建专门的结局生成函数
        #     ending, _ = generate_next_decision(
        #         st.session_state.selected_npc,
        #         st.session_state.player,
        #         tags,
        #         st.session_state.player_outfits,
        #         st.session_state.intro,
        #         st.session_state.dialogue_history
        #     )
        
        # st.subheader(f"{st.session_state.selected_npc['name']} 的回应")
        # st.markdown(ending)
        
        # 保存游戏记录
        # 创建logs目录（如果不存在）
        if not os.path.exists("logs"):
            os.makedirs("logs")
        
        # 保存对话历史
        log_data = {
            "player": st.session_state.player,
            "npc": st.session_state.selected_npc,
            "player_outfits": st.session_state.player_outfits,
            "dialogue_history": st.session_state.dialogue_history,
            "model": st.session_state.chat_model,
            "prompts": st.session_state.prompts,
            "skills": st.session_state.skills,
            "npcs": st.session_state.npcs,
            "place": st.session_state.place,
            "places": st.session_state.places,
            "outfits": st.session_state.outfits_data
        }

        from datetime import datetime
        now = datetime.now()
        formatted = now.strftime("%Y%m%d-%H%M%S")
        
        save_data(log_data, f"logs/{formatted}_{st.session_state.place['name']}_{st.session_state.selected_npc['name']}.json")
        st.success("游戏记录已保存！")
        # 开放所有相关信息的下载
        provided_data = json.dumps(log_data, ensure_ascii=False, indent=4)
        st.download_button(
            label="下载过程文件",
            data=provided_data,
            file_name=f"logs_{formatted}_{st.session_state.place['name']}_{st.session_state.selected_npc['name']}.json",
            mime="application/json",
        )
        
        if st.button("返回主菜单"):
            # 重置游戏状态
            skills, npcs, outfits_data, player, prompts, places = load_game_data()
            st.session_state.game_state = 'intro'
            st.session_state.selected_npc = None
            st.session_state.player_outfits = {}
            st.session_state.outfit_rolls = 0
            st.session_state.current_outfits = {}
            st.session_state.max_decisions = 0
            st.session_state.dialogue_history = []
            st.session_state.current_dialogue = ""
            st.session_state.current_choices = []
            st.session_state.player = player
            st.session_state.chat_rounds = 0
            st.session_state.skills = skills
            st.session_state.prompts = prompts
            st.session_state.npcs = npcs
            st.session_state.outfits_data = outfits_data
            st.session_state.places = places
            st.rerun()

if __name__ == "__main__":
    asyncio.run(main())
